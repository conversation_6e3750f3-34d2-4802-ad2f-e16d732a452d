import React, {useEffect, useState} from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Text,
    TextInput,
    Platform,
    Alert,
} from 'react-native';
import PhoneInput, {
    getAllCountries,
} from 'react-native-international-phone-number';
import type {ICountry} from 'react-native-international-phone-number';
import Clipboard from '@react-native-clipboard/clipboard';
import DeviceCountry from 'react-native-device-country';

const isValidCountryCode = (code: string) => /^\+?\d{1,4}$/.test(code);

interface PhoneInputProps {
    onChange: (phoneNumber: string, isValid: boolean) => void;
}

const PhoneNumberInput: React.FC<PhoneInputProps> = ({onChange}) => {
    const [value, setValue] = useState('');
    const [isValid, setIsValid] = useState(false);
    const [selectedCountry, setSelectedCountry] = useState<null | ICountry>(
        getAllCountries().find(c => c.cca2 === 'US') || null,
    );
    const [customCode, setCustomCode] = useState('');
    const [useCustomCode, setUseCustomCode] = useState(false);

    useEffect(() => {
        console.log('Getting device country...');
        DeviceCountry.getCountryCode()
            .then(countryCode => {
                const country = getAllCountries().find(
                    c => c.cca2 === countryCode.code.toUpperCase(),
                );
                if (country) {
                    setSelectedCountry(country);
                }
                console.log('Device country set to:', country?.name || 'US');
            })
            .catch(error => {
                console.error('Failed to get device country:', error.message);
            });
    }, []);

    const handleChange = (phoneNumber: string) => {
        console.log(
            'Phone number changed:',
            phoneNumber,
            useCustomCode ? customCode : selectedCountry?.callingCode,
        );
        phoneNumber = phoneNumber
            .replace(/^\+/, '')
            .replace(/^0+/, '')
            .replace(/\s+/g, '');
        setValue(phoneNumber);
        const checkValid = /^\d{6,15}$/.test(phoneNumber.trim());
        setIsValid(checkValid);

        let countryCode;
        if (useCustomCode) {
            countryCode = customCode.startsWith('+')
                ? customCode
                : `+${customCode}`;
        } else {
            countryCode = selectedCountry?.callingCode || '+1';
        }

        const cleanCountryCode = countryCode.replace(/^\+/, '');
        const formattedNumber = `+${cleanCountryCode}${phoneNumber}`;

        onChange(
            formattedNumber,
            checkValid && (!useCustomCode || isValidCountryCode(customCode)),
        );
    };

    const handleCustomCodeChange = (code: string) => {
        const cleanCode = code.replace(/[^\d+]/g, '');
        setCustomCode(cleanCode);
        if (cleanCode) {
            setUseCustomCode(true);
            setSelectedCountry(null);
        } else {
            setUseCustomCode(false);
        }
        handleChange(value);
    };

    const handleCountryChange = (country: ICountry) => {
        setSelectedCountry(country);
        setUseCustomCode(false);
        setCustomCode('');
    };

    const handlePaste = async () => {
        try {
            console.log('Reading clipboard...');
            const clipboardContent = await Clipboard.getString();
            const cleanNumber = clipboardContent.trim().replace(/\s+/g, '');

            const isPhoneNumber = /^\+?\d{6,15}$/.test(cleanNumber);
            if (!isPhoneNumber) {
                Alert.alert(
                    'Invalid Content',
                    'Clipboard content is not a valid phone number',
                );
                console.log(
                    'Clipboard content is not a phone number:',
                    cleanNumber,
                );
                return;
            }

            // Scenario 1: International number is pasted (e.g., +447...)
            if (cleanNumber.startsWith('+') || cleanNumber.startsWith('00')) {
                const numbersOnly = cleanNumber.startsWith('+')
                    ? cleanNumber.substring(1)
                    : cleanNumber.substring(2);
                let matchedCountry: ICountry | null = null;
                const countries = getAllCountries();

                const sortedCountries = countries.sort(
                    (a, b) =>
                        b.callingCode.replace('+', '').length -
                        a.callingCode.replace('+', '').length,
                );

                for (const country of sortedCountries) {
                    const code = country.callingCode.replace('+', '');
                    if (numbersOnly.startsWith(code)) {
                        matchedCountry = country;
                        break;
                    }
                }

                console.log(
                    'Matched country from international paste:',
                    matchedCountry?.cca2,
                );

                if (matchedCountry) {
                    const code = matchedCountry.callingCode.replace(/^\+/, '');
                    const nationalNumber = numbersOnly.substring(code.length);

                    setSelectedCountry(matchedCountry);
                    setValue(nationalNumber);
                    setUseCustomCode(false);
                    setCustomCode('');

                    const formattedNumber = `+${code}${nationalNumber}`;
                    const nationalNumberIsValid = /^\d{6,15}$/.test(
                        nationalNumber,
                    );
                    setIsValid(nationalNumberIsValid);
                    onChange(formattedNumber, nationalNumberIsValid);
                } else {
                    // Fallback for international numbers with no matching country
                    const possibleCode = numbersOnly.substring(0, 3);
                    const remainingNumber = numbersOnly.substring(
                        possibleCode.length,
                    );

                    setCustomCode(`+${possibleCode}`);
                    setValue(remainingNumber);
                    setUseCustomCode(true);
                    setSelectedCountry(null);

                    const formattedNumber = `+${possibleCode}${remainingNumber}`;
                    const remainingIsValid = /^\d{6,15}$/.test(remainingNumber);
                    setIsValid(remainingIsValid);
                    onChange(formattedNumber, remainingIsValid);
                }
            }
            // Scenario 2: Local number is pasted (e.g., 07...)
            else {
                console.log(
                    'Pasted a local number. Assuming it belongs to the selected country.',
                );

                // Treat the entire string as the national number
                setValue(cleanNumber);
                const localNumberIsValid = /^\d{6,15}$/.test(cleanNumber);
                setIsValid(localNumberIsValid);

                // Get the currently active country code
                let countryCode;
                if (useCustomCode) {
                    countryCode = customCode.startsWith('+')
                        ? customCode
                        : `+${customCode}`;
                } else {
                    countryCode = selectedCountry?.callingCode || '+1';
                }

                const cleanCountryCode = countryCode.replace(/^\+/, '');
                const formattedNumber = `+${cleanCountryCode}${cleanNumber}`;

                // Trigger the top-level onChange with the full number and validity
                onChange(
                    formattedNumber,
                    localNumberIsValid &&
                        (!useCustomCode || isValidCountryCode(customCode)),
                );
            }
        } catch (error) {
            console.error('Failed to read clipboard:', error);
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.inputRow}>
                {!useCustomCode ? (
                    <View style={styles.phoneInputWrapper}>
                        <PhoneInput
                            value={value}
                            defaultCountry={'US'}
                            onChangePhoneNumber={handleChange}
                            selectedCountry={selectedCountry}
                            onChangeSelectedCountry={handleCountryChange}
                            placeholderTextColor="#718096"
                            placeholder="Phone number"
                        />
                    </View>
                ) : (
                    <View style={styles.customCodeContainer}>
                        <TextInput
                            style={styles.customCodeInput}
                            value={customCode}
                            onChangeText={handleCustomCodeChange}
                            placeholder="+XXX"
                            keyboardType="phone-pad"
                            maxLength={5}
                            placeholderTextColor="#718096"
                        />
                        <TextInput
                            style={[styles.textInput, styles.phoneNumberInput]}
                            value={value}
                            onChangeText={handleChange}
                            placeholder="Phone number"
                            keyboardType="phone-pad"
                            placeholderTextColor="#718096"
                        />
                    </View>
                )}
            </View>
            <View style={styles.buttonRow}>
                <TouchableOpacity
                    style={[
                        styles.button,
                        useCustomCode ? styles.buttonActive : null,
                    ]}
                    onPress={() => setUseCustomCode(!useCustomCode)}>
                    <Text
                        style={[
                            styles.buttonText,
                            useCustomCode ? styles.buttonTextActive : null,
                        ]}>
                        {useCustomCode ? 'Use Country List' : 'Use Custom Code'}
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.button, styles.pasteButton]}
                    onPress={handlePaste}>
                    <Text style={[styles.buttonText, styles.pasteButtonText]}>
                        Paste
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
    },
    inputRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 16,
    },
    phoneInputWrapper: {
        flex: 1,
        height: 56,
        overflow: 'hidden',
        ...Platform.select({
            ios: {
                shadowColor: '#2D3748',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.05,
                shadowRadius: 4,
            },
            android: {
                elevation: 2,
            },
        }),
    },
    customCodeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
        width: '100%',
    },
    customCodeInput: {
        width: 90,
        height: 56,
        borderWidth: 1,
        borderColor: '#E2E8F0',
        borderRadius: 12,
        backgroundColor: '#ffffff',
        paddingHorizontal: 12,
        fontSize: 16,
        color: '#2D3748',
        ...Platform.select({
            ios: {
                shadowColor: '#2D3748',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.05,
                shadowRadius: 4,
            },
            android: {
                elevation: 2,
            },
        }),
    },
    phoneNumberInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#E2E8F0',
        borderRadius: 12,
        backgroundColor: '#ffffff',
        ...Platform.select({
            ios: {
                shadowColor: '#2D3748',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.05,
                shadowRadius: 4,
            },
            android: {
                elevation: 2,
            },
        }),
    },
    buttonRow: {
        flexDirection: 'row',
        gap: 12,
    },
    button: {
        flex: 1,
        padding: 12,
        backgroundColor: '#EDF2F7',
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        height: 44,
        ...Platform.select({
            ios: {
                shadowColor: '#2D3748',
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.05,
                shadowRadius: 4,
            },
            android: {
                elevation: 2,
            },
        }),
    },
    buttonActive: {
        backgroundColor: '#4299E1',
    },
    pasteButton: {
        backgroundColor: '#EDF2F7',
    },
    buttonText: {
        color: '#4A5568',
        fontSize: 14,
        fontWeight: '600',
    },
    buttonTextActive: {
        color: '#ffffff',
    },
    pasteButtonText: {
        color: '#4A5568',
    },
    textInput: {
        height: 56,
        fontSize: 16,
        color: '#2D3748',
        paddingHorizontal: 12,
    },
});

export default PhoneNumberInput;
