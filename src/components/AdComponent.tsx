import React, {useEffect, useState} from 'react';
import {Image, Text, View, StyleSheet} from 'react-native';
import {
    NativeAd,
    NativeAdView,
    NativeAsset,
    NativeAssetType,
    NativeMediaView,
    BannerAd,
    BannerAdSize,
} from 'react-native-google-mobile-ads';

interface NativeComponentProps {
    nativeId: string;
    bannerId: string;
    showAdMedia?: boolean;
    backgroundColor?: string;
}

export const AdComponent: React.FC<NativeComponentProps> = ({
    nativeId,
    bannerId,
    showAdMedia,
    backgroundColor,
}) => {
    const [nativeAd, setNativeAd] = useState<NativeAd>();
    const [hasError, setHasError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const outerContainerStyle = {
        ...styles.outerContainer,
        backgroundColor,
    };

    useEffect(() => {
        setIsLoading(true);
        setHasError(false);

        NativeAd.createForAdRequest(nativeId)
            .then(ad => {
                setNativeAd(ad);
                setHasError(false);
            })
            .catch(error => {
                console.error('Failed to load native ad:', error);
                setHasError(true);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [nativeId]);

    if (isLoading || hasError || !nativeAd) {
        return (
            <View
                style={[
                    outerContainerStyle,
                    {minHeight: showAdMedia ? 450 : 80},
                ]}>
                <BannerAd
                    unitId={bannerId}
                    size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
                    requestOptions={{
                        requestNonPersonalizedAdsOnly: true,
                    }}
                />
            </View>
        );
    }
    ``;
    return (
        <View
            style={[outerContainerStyle, {minHeight: showAdMedia ? 450 : 80}]}>
            <View
                style={[
                    styles.outerContainer,
                    {minHeight: showAdMedia ? 450 : 80},
                ]}>
                <NativeAdView nativeAd={nativeAd} style={styles.adContainer}>
                    <View
                        style={[
                            styles.mainContainer,
                            !showAdMedia && styles.compactContainer,
                        ]}>
                        <View style={styles.headerContainer}>
                            {nativeAd.icon && (
                                <NativeAsset assetType={NativeAssetType.ICON}>
                                    <Image
                                        source={{uri: nativeAd.icon.url}}
                                        style={[
                                            styles.icon,
                                            !showAdMedia && styles.compactIcon,
                                        ]}
                                    />
                                </NativeAsset>
                            )}
                            <View style={styles.titleContainer}>
                                <NativeAsset
                                    assetType={NativeAssetType.HEADLINE}>
                                    <Text
                                        style={[
                                            styles.headline,
                                            !showAdMedia &&
                                                styles.compactHeadline,
                                        ]}>
                                        {nativeAd.headline}
                                    </Text>
                                </NativeAsset>
                                {nativeAd.advertiser && showAdMedia && (
                                    <NativeAsset
                                        assetType={NativeAssetType.ADVERTISER}>
                                        <Text style={styles.advertiser}>
                                            {nativeAd.advertiser}
                                        </Text>
                                    </NativeAsset>
                                )}
                                {nativeAd.starRating !== null &&
                                    nativeAd.starRating > 0 &&
                                    showAdMedia && (
                                        <NativeAsset
                                            assetType={
                                                NativeAssetType.STAR_RATING
                                            }>
                                            <Text style={styles.rating}>
                                                Rating:{' '}
                                                {nativeAd.starRating.toFixed(1)}{' '}
                                                ★
                                            </Text>
                                        </NativeAsset>
                                    )}
                            </View>
                            <Text style={styles.sponsoredLabel}>Ad</Text>
                        </View>

                        {showAdMedia && (
                            <NativeMediaView style={styles.mediaView} />
                        )}

                        {showAdMedia && nativeAd.body && (
                            <NativeAsset assetType={NativeAssetType.BODY}>
                                <Text style={styles.body}>{nativeAd.body}</Text>
                            </NativeAsset>
                        )}

                        {nativeAd.callToAction && (
                            <NativeAsset
                                assetType={NativeAssetType.CALL_TO_ACTION}>
                                <View style={styles.ctaButton}>
                                    <Text style={styles.ctaText}>
                                        {nativeAd.callToAction}
                                    </Text>
                                </View>
                            </NativeAsset>
                        )}
                    </View>
                </NativeAdView>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    outerContainer: {
        width: '100%',
    },
    adContainer: {
        backgroundColor: '#fff',
        // padding: 16,
        borderRadius: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.2,
        shadowRadius: 2,
        width: '100%',
    },
    mainContainer: {
        // width: '100%',
        margin: 16,
    },
    compactContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 8,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        marginBottom: 8,
        minHeight: 60,
        paddingVertical: 8,
        overflow: 'hidden',
    },
    icon: {
        width: 40,
        height: 40,
        borderRadius: 8,
        marginRight: 12,
    },
    compactIcon: {
        width: 32,
        height: 32,
    },
    titleContainer: {
        flex: 1,
    },
    headline: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#000',
        marginBottom: 4,
        lineHeight: 20,
    },
    compactHeadline: {
        fontSize: 14,
        marginBottom: 0,
    },
    advertiser: {
        fontSize: 12,
        color: '#666',
        marginTop: 2,
    },
    sponsoredLabel: {
        fontSize: 12,
        color: '#666',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
        overflow: 'hidden',
        marginLeft: 8,
    },
    mediaView: {
        width: '100%',
        aspectRatio: 16 / 9,
        maxHeight: 250,
        marginBottom: 16,
        borderRadius: 8,
        overflow: 'hidden',
    },
    body: {
        fontSize: 14,
        color: '#444',
        marginTop: 12,
        marginBottom: 16,
        lineHeight: 20,
        flexShrink: 1,
        minHeight: 60,
    },
    ctaButton: {
        backgroundColor: '#2196F3',
        borderRadius: 6,
        padding: 12,
        alignItems: 'center',
        marginTop: 12,
        minWidth: 120,
    },
    ctaText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold',
    },
    rating: {
        fontSize: 12,
        color: '#666',
        fontWeight: '500',
        marginTop: 2,
    },
});
